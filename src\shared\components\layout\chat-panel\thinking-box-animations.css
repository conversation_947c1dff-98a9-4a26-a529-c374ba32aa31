/* InlineThinkingBox Animation Styles */

/* Keyframes cho enter animation */
@keyframes thinkingBoxEnter {
  0% {
    opacity: 0;
    transform: translateY(16px) scale(0.9);
  }
  50% {
    opacity: 0.7;
    transform: translateY(-2px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Keyframes cho leave animation */
@keyframes thinkingBoxLeave {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translateY(4px) scale(0.98);
  }
  100% {
    opacity: 0;
    transform: translateY(12px) scale(0.9);
  }
}

/* Keyframes cho content fade in */
@keyframes contentFadeIn {
  0% {
    opacity: 0;
    transform: translateY(8px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Keyframes cho icon pulse */
@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* Keyframes cho progress bar */
@keyframes progressFlow {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Animation classes */
.thinking-box-enter {
  animation: thinkingBoxEnter 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.thinking-box-leave {
  animation: thinkingBoxLeave 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.thinking-box-content-enter {
  animation: contentFadeIn 0.3s ease-out 0.1s both;
}

.thinking-box-icon-pulse {
  animation: iconPulse 2s ease-in-out infinite;
}

.thinking-box-progress {
  position: relative;
  overflow: hidden;
}

.thinking-box-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.3),
    transparent
  );
  animation: progressFlow 2s ease-in-out infinite;
}

/* Hover effects */
.thinking-box-container:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.thinking-box-container {
  transition: all 0.2s ease-out;
}

/* Responsive animations */
@media (prefers-reduced-motion: reduce) {
  .thinking-box-enter,
  .thinking-box-leave,
  .thinking-box-content-enter,
  .thinking-box-icon-pulse,
  .thinking-box-progress::after {
    animation: none;
  }
  
  .thinking-box-container:hover {
    transform: none;
  }
}

/* Dark mode adjustments */
.dark .thinking-box-progress::after {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(96, 165, 250, 0.3),
    transparent
  );
}

.dark .thinking-box-container:hover {
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.1);
}
