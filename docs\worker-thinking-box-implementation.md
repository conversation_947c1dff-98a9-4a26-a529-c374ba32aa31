# Worker Thinking Box Implementation

## 📋 **Tóm tắt thay đổi**

Thực hiện 3 yêu cầu ch<PERSON>:

1. **Thay đổi hiển thị worker messages**: Từ messages bình thường → InlineThinkingBox trong chat flow
2. **Sửa lỗi avatar**: Chỉ tin nhắn mới có avatar mới khi đổi agent (không thay đổi avatar tin nhắn cũ)
3. **Bỏ avatar ở reply section** trong ChatInput

## 🎯 **1. Worker Thinking Box Implementation**

### **1.1. Tạo InlineThinkingBox Component**
- **File**: `src/shared/components/layout/chat-panel/InlineThinkingBox.tsx`
- **Chức năng**:
  - Hiển thị worker messages inline trong chat flow ngay sau tin nhắn user cuối cùng
  - Tự động ẩn sau 2000ms khi hoàn thành hoặc disconnect
  - Hỗ trợ streaming animation và progress bar
  - Responsive design với animation fade in/out
  - Thiết kế compact phù hợp với chat flow

### **1.2. C<PERSON><PERSON> nhật useChatStream**
- **File**: `src/shared/hooks/common/useChatStream.ts`
- **Thay đổi**:
  - Thêm `workerThinking` state để track worker messages
  - Xử lý worker tokens riêng biệt (không tạo messages)
  - Worker tokens được gửi vào thinking box thay vì message list
  - Reset thinking box khi stream session end

```typescript
// Worker thinking state
const [workerThinking, setWorkerThinking] = useState({
  isVisible: false,
  content: '',
  isStreaming: false
});

// Xử lý worker messages riêng
if (role === 'worker') {
  setWorkerThinking(prev => ({
    isVisible: true,
    content: prev.content + text,
    isStreaming: true
  }));
  return; // Không tạo message
}
```

### **1.3. Tích hợp vào ChatContent**
- **File**: `src/shared/components/layout/chat-panel/ChatContent.tsx`
- **Vị trí**: Đặt InlineThinkingBox ngay sau tin nhắn user cuối cùng trong chat flow
- **Props**: Nhận `workerThinking` từ ChatPanel và truyền vào InlineThinkingBox
- **Logic**: Tự động tìm tin nhắn user cuối cùng và hiển thị thinking box sau đó

## 🔧 **2. Avatar Fix Implementation**

### **2.1. Thêm agentId vào Message Metadata**
- **File**: `src/shared/hooks/common/useChatStream.ts`
- **Thay đổi**:
  - Thêm `currentAgentId` state để track agent hiện tại
  - Lưu `agentId` vào metadata khi tạo message mới
  - Chỉ tin nhắn mới có agentId của agent hiện tại

```typescript
// Lưu agentId vào metadata
metadata: {
  ...(currentAgentId && { agentId: currentAgentId })
}
```

### **2.2. Cập nhật Avatar Logic trong ChatContent**
- **File**: `src/shared/components/layout/chat-panel/ChatContent.tsx`
- **Logic mới**:
  - Sử dụng `agentId` từ message metadata thay vì global `agentAvatar`
  - Tin nhắn cũ giữ nguyên avatar cũ
  - Tin nhắn mới sử dụng avatar mới

```typescript
// Sử dụng agentId từ message metadata
if (messageData.sender === 'supervisor') {
  if (messageData.metadata?.agentId) {
    // TODO: Implement getAgentAvatarById service
    return agentAvatar ? { avatar: agentAvatar } : {};
  }
  return agentAvatar ? { avatar: agentAvatar } : {};
}
```

## 🚫 **3. Remove Avatar from Reply Section**

### **3.1. Cập nhật ReplyPreview Component**
- **File**: `src/shared/components/common/ReplyPreview/ReplyPreview.tsx`
- **Thay đổi**: Bỏ avatar section trong reply preview
- **Kết quả**: Reply section chỉ hiển thị text content, không có avatar

## 🌐 **4. Localization Support**

### **4.1. Tạo Chat Locales**
- **Files**: 
  - `src/shared/locales/chat/vi.json`
  - `src/shared/locales/chat/en.json`
  - `src/shared/locales/chat/zh.json`
  - `src/shared/locales/chat/index.ts`

### **4.2. Thêm vào i18n Config**
- **File**: `src/lib/i18n.ts`
- **Thay đổi**: Import và thêm chat locales vào resources

### **4.3. Keys được thêm**
```json
{
  "chat": {
    "thinking": {
      "title": "Đang suy nghĩ...",
      "processing": "Đang xử lý yêu cầu của bạn",
      "completed": "Hoàn thành",
      "waiting": "Đang chờ phản hồi..."
    }
  }
}
```

## 📁 **Files Modified**

### **New Files**
- `src/shared/components/layout/chat-panel/InlineThinkingBox.tsx`
- `src/shared/locales/chat/vi.json`
- `src/shared/locales/chat/en.json`
- `src/shared/locales/chat/zh.json`
- `src/shared/locales/chat/index.ts`

### **Modified Files**
- `src/shared/hooks/common/useChatStream.ts`
- `src/shared/components/layout/chat-panel/ChatContent.tsx`
- `src/shared/components/layout/chat-panel/ChatPanel.tsx`
- `src/shared/components/common/ReplyPreview/ReplyPreview.tsx`
- `src/shared/types/chat-streaming.types.ts` (đã có agentId trong metadata)
- `src/lib/i18n.ts`

## 🎨 **UI/UX Improvements**

### **InlineThinkingBox Features**
- ✅ Inline display trong chat flow
- ✅ Fade in/out animations
- ✅ Streaming text with cursor
- ✅ Progress indicator
- ✅ Auto-hide after 2000ms
- ✅ Responsive design
- ✅ Dark mode support
- ✅ Close button (when not streaming)
- ✅ Compact design phù hợp với chat interface
- ✅ Hiển thị ngay sau tin nhắn user cuối cùng

### **Avatar Behavior**
- ✅ Tin nhắn cũ giữ nguyên avatar
- ✅ Tin nhắn mới sử dụng avatar hiện tại
- ✅ Không thay đổi retroactive

### **Reply Section**
- ✅ Bỏ avatar, chỉ hiển thị text
- ✅ Gọn gàng hơn
- ✅ Focus vào nội dung

## 🔄 **Flow Hoạt động**

### **Worker Message Flow**
1. **SSE nhận worker token** → `onTextToken(text, 'worker')`
2. **useChatStream xử lý** → Update `workerThinking.content`
3. **ChatContent render** → Hiển thị InlineThinkingBox ngay sau tin nhắn user cuối cùng
4. **Worker stream end** → Set `isStreaming: false`
5. **Auto-hide sau 2000ms** → InlineThinkingBox biến mất

### **Avatar Flow**
1. **User đổi agent** → `setCurrentAgentId(newAgentId)`
2. **Tin nhắn mới được tạo** → Lưu `agentId` vào metadata
3. **ChatContent render** → Sử dụng agentId từ metadata
4. **Tin nhắn cũ** → Giữ nguyên avatar cũ
5. **Tin nhắn mới** → Sử dụng avatar mới

## ✅ **Testing Checklist**

- [ ] Worker messages hiển thị trong InlineThinkingBox
- [ ] InlineThinkingBox tự ẩn sau 2000ms
- [ ] InlineThinkingBox hiển thị ngay sau tin nhắn user cuối cùng
- [ ] Tin nhắn cũ giữ nguyên avatar khi đổi agent
- [ ] Tin nhắn mới có avatar mới
- [ ] Reply section không hiển thị avatar
- [ ] Localization hoạt động đúng
- [ ] Responsive trên mobile/tablet
- [ ] Dark mode hoạt động
- [ ] Animation smooth
