/**
 * ThinkingBox Component
 * Hiển thị worker messages trong một ô thinking ở giữa màn hình
 * Tự động ẩn sau 2000ms khi hoàn thành hoặc disconnect
 */

import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface ThinkingBoxProps {
  /**
   * Nội dung thinking text
   */
  content: string;

  /**
   * Có đang hiển thị hay không
   */
  isVisible: boolean;

  /**
   * Callback khi thinking box được ẩn
   */
  onHide?: () => void;

  /**
   * Có đang streaming hay không
   */
  isStreaming?: boolean;
}

/**
 * Component hiển thị thinking box cho worker messages
 */
const ThinkingBox: React.FC<ThinkingBoxProps> = ({
  content,
  isVisible,
  onHide,
  isStreaming = false
}) => {
  const { t } = useTranslation();
  const [shouldShow, setShouldShow] = useState(isVisible);
  const [isAnimating, setIsAnimating] = useState(false);

  // Handle visibility changes
  useEffect(() => {
    if (isVisible) {
      setShouldShow(true);
      setIsAnimating(true);
    } else {
      // Delay hiding để có animation
      setIsAnimating(false);
      const hideTimeout = setTimeout(() => {
        setShouldShow(false);
        onHide?.();
      }, 2000); // Ẩn sau 2000ms

      return () => clearTimeout(hideTimeout);
    }
  }, [isVisible, onHide]);

  // Auto hide khi không streaming và có content
  useEffect(() => {
    if (!isStreaming && content && isVisible) {
      const autoHideTimeout = setTimeout(() => {
        setIsAnimating(false);
        const hideTimeout = setTimeout(() => {
          setShouldShow(false);
          onHide?.();
        }, 2000);

        return () => clearTimeout(hideTimeout);
      }, 1000); // Đợi 1s sau khi stop streaming

      return () => clearTimeout(autoHideTimeout);
    }
  }, [isStreaming, content, isVisible, onHide]);

  if (!shouldShow) {
    return null;
  }

  return (
    <div className={`
      fixed inset-0 z-50 flex items-center justify-center p-4
      transition-all duration-300 ease-in-out
      ${isAnimating ? 'opacity-100' : 'opacity-0'}
    `}>
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/20 dark:bg-black/40" />
      
      {/* Thinking Box */}
      <div className={`
        relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700
        max-w-md w-full mx-auto p-6 transform transition-all duration-300 ease-in-out
        ${isAnimating ? 'scale-100 translate-y-0' : 'scale-95 translate-y-4'}
      `}>
        {/* Header */}
        <div className="flex items-center justify-center mb-4">
          <div className="flex items-center gap-3">
            {/* Thinking Icon */}
            <div className="relative">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              
              {/* Pulsing animation */}
              {isStreaming && (
                <div className="absolute inset-0 w-8 h-8 bg-blue-400 rounded-full animate-ping opacity-20" />
              )}
            </div>

            {/* Title */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('chat:thinking.title', 'Đang suy nghĩ...')}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isStreaming 
                  ? t('chat:thinking.processing', 'Đang xử lý yêu cầu của bạn')
                  : t('chat:thinking.completed', 'Hoàn thành')
                }
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 min-h-[100px] max-h-[300px] overflow-y-auto">
          {content ? (
            <div className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap leading-relaxed">
              {content}
              {isStreaming && (
                <span className="inline-block w-2 h-4 bg-blue-500 ml-1 animate-pulse" />
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
                <span className="text-sm">{t('chat:thinking.waiting', 'Đang chờ phản hồi...')}</span>
              </div>
            </div>
          )}
        </div>

        {/* Progress bar */}
        {isStreaming && (
          <div className="mt-4">
            <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1">
              <div className="bg-blue-500 h-1 rounded-full animate-pulse" style={{ width: '60%' }} />
            </div>
          </div>
        )}

        {/* Close button (chỉ hiển thị khi không streaming) */}
        {!isStreaming && (
          <button
            onClick={() => {
              setIsAnimating(false);
              setTimeout(() => {
                setShouldShow(false);
                onHide?.();
              }, 300);
            }}
            className="absolute top-3 right-3 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
};

export default ThinkingBox;
