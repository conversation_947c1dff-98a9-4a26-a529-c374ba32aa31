/**
 * InlineThinkingBox Component
 * Hiển thị worker messages inline trong chat flow
 * Tự động ẩn sau 2000ms khi hoàn thành hoặc disconnect
 */

import React, { useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import '@/shared/styles/scrollbar.css';

interface InlineThinkingBoxProps {
  /**
   * Nội dung thinking text
   */
  content: string;

  /**
   * Có đang hiển thị hay không
   */
  isVisible: boolean;

  /**
   * Callback khi thinking box được ẩn
   */
  onHide?: () => void;

  /**
   * Có đang streaming hay không
   */
  isStreaming?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị thinking box inline trong chat flow
 */
const InlineThinkingBox: React.FC<InlineThinkingBoxProps> = ({
  content,
  isVisible,
  onHide,
  isStreaming = false,
  className = ''
}) => {
  const { t } = useTranslation();
  const [shouldShow, setShouldShow] = useState(isVisible);
  const [isAnimating, setIsAnimating] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // Handle visibility changes
  useEffect(() => {
    if (isVisible) {
      setShouldShow(true);
      setIsAnimating(true);
      return; // Explicit return for this case
    } else {
      // Delay hiding để có animation
      setIsAnimating(false);
      const hideTimeout = setTimeout(() => {
        setShouldShow(false);
        onHide?.();
      }, 2000); // Ẩn sau 2000ms

      return () => clearTimeout(hideTimeout);
    }
  }, [isVisible, onHide]);

  // Auto hide khi không streaming và có content
  useEffect(() => {
    if (!isStreaming && content && isVisible) {
      const autoHideTimeout = setTimeout(() => {
        setIsAnimating(false);
        const hideTimeout = setTimeout(() => {
          setShouldShow(false);
          onHide?.();
        }, 2000);

        return () => clearTimeout(hideTimeout);
      }, 1000); // Đợi 1s sau khi stop streaming

      return () => clearTimeout(autoHideTimeout);
    }
    return; // Explicit return for other cases
  }, [isStreaming, content, isVisible, onHide]);

  // Auto-scroll khi content thay đổi và đang streaming
  useEffect(() => {
    if (isStreaming && content && contentRef.current) {
      // Kiểm tra xem user có đang scroll lên không
      const element = contentRef.current;
      const isNearBottom = element.scrollHeight - element.scrollTop - element.clientHeight < 10;

      // Chỉ auto-scroll nếu user đang ở gần bottom hoặc đây là content đầu tiên
      if (isNearBottom || element.scrollTop === 0) {
        // Smooth scroll to bottom
        element.scrollTo({
          top: element.scrollHeight,
          behavior: 'smooth'
        });
      }
    }
  }, [content, isStreaming]);

  if (!shouldShow) {
    return null;
  }

  return (
    <div className={`
      w-full transition-all duration-300 ease-in-out
      ${isAnimating ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'}
      ${className}
    `}>
      {/* Inline Thinking Box */}
      <div className={`
        bg-blue-50 dark:bg-blue-950/30 rounded-xl border border-blue-200 dark:border-blue-800/50
        p-4 mx-auto max-w-2xl transform transition-all duration-300 ease-in-out
        ${isAnimating ? 'scale-100' : 'scale-95'}
      `}>
        {/* Header */}
        <div className="flex items-center gap-3 mb-3">
          {/* Thinking Icon */}
          <div className="relative flex-shrink-0">
            <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>

            {/* Pulsing animation */}
            {isStreaming && (
              <div className="absolute inset-0 w-6 h-6 bg-blue-400 rounded-full animate-ping opacity-20" />
            )}
          </div>

          {/* Title */}
          <div className="flex-1">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
              {t('chat:thinking.title', 'Đang suy nghĩ...')}
            </h4>
            <p className="text-xs text-blue-600 dark:text-blue-400">
              {isStreaming
                ? t('chat:thinking.processing', 'Đang xử lý yêu cầu của bạn')
                : t('chat:thinking.completed', 'Hoàn thành')
              }
            </p>
          </div>

          {/* Close button (chỉ hiển thị khi không streaming) */}
          {!isStreaming && (
            <button
              onClick={() => {
                setIsAnimating(false);
                setTimeout(() => {
                  setShouldShow(false);
                  onHide?.();
                }, 300);
              }}
              className="flex-shrink-0 p-1 text-blue-400 hover:text-blue-600 dark:hover:text-blue-300 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        {/* Content */}
        <div
          ref={contentRef}
          className="bg-blue-100/50 dark:bg-blue-900/30 rounded-lg p-3 min-h-[60px] max-h-[200px] overflow-y-auto thinking-box-scrollbar"
          style={{
            scrollBehavior: 'smooth'
          }}
        >
          {content ? (
            <div className="text-sm text-blue-800 dark:text-blue-200 whitespace-pre-wrap leading-relaxed">
              {content}
              {isStreaming && (
                <span className="inline-block w-1.5 h-3 bg-blue-500 ml-1 animate-pulse" />
              )}
            </div>
          ) : (
            <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
              <div className="flex gap-1">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
              <span className="text-xs">{t('chat:thinking.waiting', 'Đang chờ phản hồi...')}</span>
            </div>
          )}
        </div>

        {/* Progress bar */}
        {isStreaming && (
          <div className="mt-3">
            <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-1">
              <div className="bg-blue-500 h-1 rounded-full animate-pulse" style={{ width: '60%' }} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InlineThinkingBox;
