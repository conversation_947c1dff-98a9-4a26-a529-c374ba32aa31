/**
 * ThreadCard Component
 * Card component hiển thị thread với start/delete actions
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { ThreadItem } from '@/shared/types/chat-streaming.types';
import { Icon, ConfirmDeleteModal } from '@/shared/components/common';
import { formatDistanceToNow } from 'date-fns';
import { vi, enUS, zhCN } from 'date-fns/locale';
import { useOptimisticUpdateThread } from '../hooks/useThreadMutations';

interface ThreadCardProps {
  /**
   * Dữ liệu thread
   */
  thread: ThreadItem;

  /**
   * Callback khi start thread (load vào chat panel)
   */
  onStart?: (threadId: string) => void;

  /**
   * Callback khi delete thread (non-active)
   */
  onDelete?: (threadId: string) => void;

  /**
   * Callback khi delete active thread (cần integration)
   */
  onDeleteActive?: (threadId: string) => void;

  /**
   * Callback khi click vào card (ch<PERSON><PERSON><PERSON> sang trang detail)
   */
  onClick?: (threadId: string) => void;

  /**
   * Thread có đang active trong chat panel không
   */
  isActive?: boolean;

  /**
   * Loading states
   */
  isStarting?: boolean;
  isDeleting?: boolean;

  /**
   * External thread update hook (optional)
   */
  externalUpdateHook?: {
    optimisticUpdate: (threadId: string, newName: string) => Promise<void>;
    isLoading: boolean;
    error: Error | null;
  };
}

/**
 * Component card hiển thị thread
 */
export const ThreadCard: React.FC<ThreadCardProps> = ({
  thread,
  onStart,
  onDelete,
  onDeleteActive,
  onClick,
  isActive = false,
  isStarting = false,
  isDeleting = false,
  externalUpdateHook
}) => {
  const { t } = useTranslation();
  const [showActions, setShowActions] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(thread.name);

  // Hook để update thread name - sử dụng external hook nếu có
  const internalUpdateHook = useOptimisticUpdateThread();
  const updateHook = externalUpdateHook || internalUpdateHook;
  const { optimisticUpdate, isLoading: isUpdating } = updateHook;

  // Update editName khi thread.name thay đổi
  useEffect(() => {
    setEditName(thread.name);
  }, [thread.name]);

  // Format thời gian với đa ngôn ngữ
  const formatTime = (timestamp: number) => {
    try {
      const { i18n } = useTranslation();
      const currentLang = i18n.language;

      // Chọn locale phù hợp
      let locale = vi; // default
      if (currentLang.startsWith('en')) locale = enUS;
      if (currentLang.startsWith('zh')) locale = zhCN;

      return formatDistanceToNow(new Date(timestamp), {
        addSuffix: true,
        locale
      });
    } catch {
      return t('common:unknown', 'Không xác định');
    }
  };

  // Xử lý click vào card - chuyển sang trang detail
  const handleCardClick = (e: React.MouseEvent) => {
    // Prevent navigation nếu đang trong các trạng thái đặc biệt
    if (isStarting || isDeleting || isUpdating || isEditing) {
      console.log('[ThreadCard] Skipping navigation - operation in progress:', {
        isStarting, isDeleting, isUpdating, isEditing
      });
      e.preventDefault();
      e.stopPropagation();
      return;
    }

    // Kiểm tra xem click có phải từ action buttons không
    const target = e.target as HTMLElement;
    const isActionButton = target.closest('button') || target.closest('[role="button"]');

    if (isActionButton) {
      console.log('[ThreadCard] Skipping navigation - clicked on action button');
      return;
    }

    console.log('[ThreadCard] Card clicked, navigating to detail:', thread.threadId);
    onClick?.(thread.threadId);
  };

  // Xử lý start thread
  const handleStart = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isStarting && !isDeleting && !isActive ) {
      console.log('startThreadMutation', thread.threadId);
      onStart?.(thread.threadId);
    }
  };

  // Xử lý delete thread
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isStarting && !isDeleting) {
      setShowDeleteModal(true);
    }
  };

  // Xử lý xác nhận xóa
  const handleConfirmDelete = () => {
    setShowDeleteModal(false);

    if (isActive) {
      // Luồng 2: Thread active - cần integration
      console.log('[ThreadCard] Deleting active thread with integration:', thread.threadId);
      onDeleteActive?.(thread.threadId);
    } else {
      // Luồng 1: Thread không active - xóa bình thường
      console.log('[ThreadCard] Deleting non-active thread:', thread.threadId);
      onDelete?.(thread.threadId);
    }
  };

  // Xử lý hủy xóa
  const handleCancelDelete = () => {
    setShowDeleteModal(false);
  };

  // Xử lý edit name
  const handleEditName = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsEditing(true);
  };

  // Xử lý save name
  const handleSaveName = async () => {
    const trimmedName = editName.trim();

    if (trimmedName && trimmedName !== thread.name) {
      try {
        console.log('[ThreadCard] Saving thread name:', { threadId: thread.threadId, oldName: thread.name, newName: trimmedName });

        // Thoát edit mode trước để tránh UI flickering
        setIsEditing(false);

        await optimisticUpdate(thread.threadId, trimmedName);
        console.log('[ThreadCard] Thread name saved successfully');

      } catch (error) {
        console.error('[ThreadCard] Failed to update thread name:', error);
        // Reset về tên cũ nếu lỗi và đảm bảo thoát edit mode
        setEditName(thread.name);
        setIsEditing(false);
      }
    } else {
      console.log('[ThreadCard] No changes or invalid name, exiting edit mode');
      setIsEditing(false);
      setEditName(thread.name);
    }
  };

  // Xử lý cancel edit
  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditName(thread.name);
  };

  return (
    <div
      className={`
        relative group p-3 sm:p-4 rounded-xl border transition-all duration-200
        ${isEditing || isDeleting ? 'cursor-default' : 'cursor-pointer'}
        ${isActive
          ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 shadow-md'
          : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-lg'
        }
        ${(isStarting || isDeleting || isUpdating) ? 'opacity-50 cursor-not-allowed' : ''}
      `}
      onClick={handleCardClick}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Header với title và actions */}
      <div className="flex items-start justify-between mb-2 sm:mb-3">
        <div className="flex-1 min-w-0 pr-2">
          {isEditing ? (
            <div className="flex items-center gap-1 sm:gap-2" onClick={(e) => e.stopPropagation()}>
              <input
                type="text"
                value={editName}
                onChange={(e) => setEditName(e.target.value)}
                className="flex-1 px-2 py-1 text-base sm:text-lg font-semibold border rounded focus:outline-none focus:ring-2 focus:ring-blue-500
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleSaveName();
                  if (e.key === 'Escape') handleCancelEdit();
                }}
                disabled={isUpdating}
                placeholder={t('threads:threads.detail.name', 'Tên cuộc hội thoại')}
              />
              <button
                onClick={handleSaveName}
                disabled={isUpdating}
                className="p-1 text-green-600 hover:text-green-700 disabled:opacity-50 flex-shrink-0"
                title={t('threads:threads.item.actions.save', 'Lưu')}
              >
                <Icon name="check" size="sm" />
              </button>
              <button
                onClick={handleCancelEdit}
                disabled={isUpdating}
                className="p-1 text-red-600 hover:text-red-700 disabled:opacity-50 flex-shrink-0"
                title={t('threads:threads.item.actions.cancel', 'Hủy')}
              >
                <Icon name="x" size="sm" />
              </button>
            </div>
          ) : (
            <div className="flex items-center gap-1 sm:gap-2">
              <h3 className={`font-semibold text-base sm:text-lg truncate flex-1 ${isActive
                ? 'text-blue-900 dark:text-blue-100'
                : 'text-gray-900 dark:text-gray-100'
                }`}>
                {thread.name}
              </h3>
              <button
                onClick={handleEditName}
                disabled={isStarting || isDeleting || isUpdating}
                className="p-1 text-gray-400 hover:text-blue-600 transition-colors opacity-0 group-hover:opacity-100 flex-shrink-0"
                title={t('threads:threads.item.actions.edit', 'Chỉnh sửa tên')}
              >
                <Icon name="edit" size="sm" />
              </button>
            </div>
          )}
        </div>

        {/* Action Icons */}
        <div className={`flex items-center gap-1 sm:gap-2 transition-opacity duration-200 ${showActions || isActive ? 'opacity-100' : 'opacity-0'
          }`}>
          {/* Start Icon */}
          <button
            onClick={handleStart}
            disabled={isStarting || isDeleting}
            className={`p-1.5 sm:p-2 rounded-lg transition-colors flex-shrink-0 ${isActive
              ? 'bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-300'
              : 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-800'
              }`}
            title={isActive ? t('threads:threads.actions.current', 'Đang hoạt động') : t('threads:threads.actions.start', 'Bắt đầu chat')}
          >
            {isStarting ? (
              <Icon name="loading" size="sm" className="animate-spin" />
            ) : (
              <Icon name={isActive ? 'check' : 'chat'} size="sm" />
            )}
          </button>

          {/* Delete Icon */}
          <button
            onClick={handleDelete}
            disabled={isStarting || isDeleting || isStarting}
            className="p-1.5 sm:p-2 rounded-lg bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400
                     hover:bg-red-200 dark:hover:bg-red-800 transition-colors flex-shrink-0"
            title={t('threads:threads.item.actions.delete', 'Xóa cuộc hội thoại')}
          >
            {isDeleting ? (
              <Icon name="loading" size="sm" className="animate-spin" />
            ) : (
              <Icon name="trash" size="sm" />
            )}
          </button>
        </div>
      </div>

      {/* Thread Info */}
      <div className="space-y-1 sm:space-y-2">
        <div className="flex items-center justify-between text-xs sm:text-sm flex-wrap gap-1">
          <span className="text-gray-500 dark:text-gray-400 truncate">
            {t('threads:threads.item.updated', 'Cập nhật {{time}}', { time: formatTime(thread.updatedAt) })}
          </span>

          {isActive && (
            <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300
                           rounded-full text-xs font-medium flex-shrink-0">
              {t('threads:status.active', 'Đang hoạt động')}
            </span>
          )}
        </div>

        <div className="text-xs text-gray-400 dark:text-gray-500 font-mono truncate">
          ID: {thread.threadId.slice(0, 8)}...
        </div>
      </div>

      {/* Active indicator */}
      {isActive && (
        <div className="absolute top-0 left-0 w-1 h-full bg-blue-500 rounded-l-xl"></div>
      )}

      {/* Loading overlay */}
      {(isStarting || isDeleting || isUpdating) && (
        <div className="absolute inset-0 bg-white/50 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
          <div className="flex items-center gap-2 text-sm font-medium">
            <Icon name="loading" size="sm" className="animate-spin" />
            <span>
              {isStarting
                ? t('threads:status.starting', 'Đang khởi động...')
                : isDeleting
                  ? t('threads:status.deleting', 'Đang xóa...')
                  : t('threads:status.updating', 'Đang cập nhật...')
              }
            </span>
          </div>
        </div>
      )}

      {/* Hover effect */}
      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/0 to-purple-500/0
                    group-hover:from-blue-500/5 group-hover:to-purple-500/5 transition-all duration-300 pointer-events-none"></div>

      {/* Confirm Delete Modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteModal}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('threads:threads.item.confirmDelete', 'Xác nhận xóa cuộc hội thoại')}
        message={t('threads:threads.item.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa cuộc hội thoại "{{name}}"?', { name: thread.name })}
        itemName={thread.name}
        isSubmitting={isDeleting}
        confirmButtonText={t('threads:threads.item.actions.delete', 'Xóa')}
        cancelButtonText={t('common:cancel', 'Hủy')}
      />
    </div>
  );
};
